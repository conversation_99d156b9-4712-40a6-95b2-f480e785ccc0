﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class TownManager : ITownService
    {
        ITownDal _townDal;

        public TownManager(ITownDal townDal)
        {
            _townDal = townDal;
        }
        [SecuredOperation("owner,admin")]
        [CacheAspect(duration: 86400)] // 24 saat - Static data, update formlarında kullanılıyor
        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<List<Town>> GetAll()
        {
            return new SuccessDataResult<List<Town>>(_townDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        [CacheAspect(duration: 86400)] // 24 saat - Static data, dropdown cascade'lerde sürekli çağrılıyor
        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<List<Town>> GetByCityId(int cityId)
        {
            return new SuccessDataResult<List<Town>>(_townDal.GetAll(c => c.CityID == cityId));
        }
    }
}
